package com.BE.service;

import com.BE.enums.PlaceholderTypeEnum;
import com.BE.enums.StatusEnum;
import com.BE.mapper.SlideDetailMapper;
import com.BE.model.entity.SlideTemplate;
import com.BE.repository.SlideDetailRepository;
import com.BE.repository.SlidePlaceholderRepository;
import com.BE.repository.SlideTemplateRepository;
import com.BE.service.implementServices.SlideDetailServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SlideDetailServiceImplTest {

    @Mock
    private SlideDetailRepository slideDetailRepository;
    
    @Mock
    private SlideTemplateRepository slideTemplateRepository;
    
    @Mock
    private SlidePlaceholderRepository slidePlaceholderRepository;
    
    @Mock
    private SlideDetailMapper slideDetailMapper;
    
    @Mock
    private ObjectMapper objectMapper;
    
    @InjectMocks
    private SlideDetailServiceImpl slideDetailService;
    
    private SlideTemplate mockSlideTemplate;
    private Set<String> validPlaceholderTypes;
    
    @BeforeEach
    void setUp() {
        mockSlideTemplate = new SlideTemplate();
        mockSlideTemplate.setId(1L);
        mockSlideTemplate.setName("Test Template");
        mockSlideTemplate.setStatus(StatusEnum.ACTIVE);
        
        validPlaceholderTypes = Arrays.stream(PlaceholderTypeEnum.values())
                .map(Enum::name)
                .collect(Collectors.toSet());
    }
    
    @Test
    void testProcessSlideDetailsFromTemplate_WithNewPlaceholderFormat() throws Exception {
        // Given
        String slideDataJson = """
            {
                "slides": [
                    {
                        "id": "slide1",
                        "title": "Test Slide",
                        "elements": [
                            {
                                "type": "text",
                                "text": "TitleName_80 Sample Title"
                            },
                            {
                                "type": "text", 
                                "text": "MainPointName_1_80 First Main Point"
                            },
                            {
                                "type": "text",
                                "text": "MainPointContent_1_120 Content for first main point"
                            },
                            {
                                "type": "text",
                                "text": "SubPointName_1_1_60 First sub point of first main point"
                            },
                            {
                                "type": "text",
                                "text": "SubPointContent_1_1_100 Content for first sub point"
                            },
                            {
                                "type": "text",
                                "text": "SubPointContent_1_2_100 Content for second sub point"
                            }
                        ]
                    }
                ]
            }
            """;
        
        when(slideTemplateRepository.findById(anyLong())).thenReturn(Optional.of(mockSlideTemplate));
        when(objectMapper.readTree(any(String.class))).thenCallRealMethod();
        
        // When & Then - Test should not throw exception
        try {
            slideDetailService.processSlideDetailsFromTemplate(1L, slideDataJson);
            System.out.println("Test passed: New placeholder format processed successfully");
        } catch (Exception e) {
            System.err.println("Test failed: " + e.getMessage());
            throw e;
        }
    }
}
